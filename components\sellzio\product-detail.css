/* Shopee-style Product Detail Layout */
.shopee-layout {
  min-height: 100vh;
  background-color: #f5f5f5;
  position: relative;
  z-index: 1; /* Lower than header */
}

/* Ensure header compatibility */
.shopee-layout .header {
  z-index: 1000 !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
}

/* Breadcrumb */
.shopee-breadcrumb {
  background: #ee4d2d;
  padding: 8px 0;
  margin-top: 60px;
  position: relative;
  z-index: 999; /* Below header but above content */
}

.shopee-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

.shopee-back-link {
  color: white;
  text-decoration: none;
  font-size: 13px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  background: none;
  border: none;
  cursor: pointer;
  font-weight: 400;
}

.shopee-back-link:hover {
  opacity: 0.8;
}

.shopee-breadcrumb-separator {
  margin: 0 6px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
}

.shopee-breadcrumb-category,
.shopee-breadcrumb-subcategory,
.shopee-breadcrumb-current {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.8);
}

.shopee-breadcrumb-current {
  color: white;
  font-weight: 400;
}

/* Main Content */
.shopee-main-content {
  padding: 20px 0;
}

.shopee-product-section {
  background: white;
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
  display: grid;
  grid-template-columns: 400px 1fr;
  gap: 40px;
}

/* Image Section */
.shopee-image-section {
  position: sticky;
  top: 80px;
  height: fit-content;
}

.shopee-main-image {
  position: relative;
  width: 100%;
  height: 400px;
  background: white;
  border: 1px solid #e5e5e5;
  border-radius: 4px;
  margin-bottom: 12px;
  overflow: hidden;
}

.shopee-mall-badge {
  position: absolute;
  top: 8px;
  left: 8px;
  background: #ee4d2d;
  color: white;
  padding: 2px 6px;
  border-radius: 2px;
  font-size: 10px;
  font-weight: 600;
  z-index: 10;
}

.shopee-discount-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background: #ff424f;
  color: white;
  padding: 2px 6px;
  border-radius: 2px;
  font-size: 10px;
  font-weight: 600;
  z-index: 10;
}

.shopee-thumbnail-gallery {
  display: flex;
  gap: 8px;
  overflow-x: auto;
}

.shopee-thumbnail {
  position: relative;
  width: 60px;
  height: 60px;
  border: 2px solid transparent;
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
  flex-shrink: 0;
}

.shopee-thumbnail.active {
  border-color: #ee4d2d;
}

.shopee-thumbnail:hover {
  border-color: #ee4d2d;
}

/* Product Info */
.shopee-product-info {
  padding: 0;
}

.shopee-product-header {
  margin-bottom: 20px;
}

.shopee-product-title {
  font-size: 20px;
  font-weight: 400;
  color: #222;
  line-height: 1.4;
  margin-bottom: 8px;
}

.shopee-special-badges {
  margin-bottom: 12px;
}

.shopee-cod-badge {
  display: inline-flex;
  align-items: center;
}

.shopee-product-stats {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
}

.shopee-rating {
  display: flex;
  align-items: center;
  gap: 4px;
}

.shopee-stars {
  display: flex;
  gap: 1px;
}

.shopee-rating-text {
  color: #ee4d2d;
  text-decoration: underline;
  cursor: pointer;
}

.shopee-divider {
  color: #ccc;
}

.shopee-reviews-count,
.shopee-sold-count {
  color: #757575;
}

.shopee-reviews-count:hover,
.shopee-sold-count:hover {
  color: #ee4d2d;
  cursor: pointer;
}

/* Price Section */
.shopee-price-section {
  background: #fafafa;
  padding: 16px;
  margin: 20px -20px;
  border-top: 1px solid #e5e5e5;
  border-bottom: 1px solid #e5e5e5;
}

.shopee-price-container {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.shopee-original-price {
  font-size: 16px;
  color: #929292;
  text-decoration: line-through;
}

.shopee-current-price {
  font-size: 30px;
  font-weight: 500;
  color: #ee4d2d;
}

.shopee-discount-percent {
  background: #ee4d2d;
  color: white;
  padding: 2px 4px;
  border-radius: 2px;
  font-size: 12px;
  font-weight: 600;
}

/* Delivery Section */
.shopee-delivery-section {
  margin: 20px 0;
  padding: 16px 0;
  border-bottom: 1px solid #e5e5e5;
}

.shopee-delivery-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.shopee-delivery-item:last-child {
  margin-bottom: 0;
}

.shopee-delivery-label {
  width: 100px;
  color: #757575;
  font-size: 14px;
  flex-shrink: 0;
}

.shopee-delivery-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #222;
  font-size: 14px;
}

/* Quantity Section */
.shopee-quantity-section {
  display: flex;
  align-items: center;
  margin: 20px 0;
  padding: 16px 0;
  border-bottom: 1px solid #e5e5e5;
}

.shopee-quantity-label {
  width: 100px;
  color: #757575;
  font-size: 14px;
  flex-shrink: 0;
}

.shopee-quantity-controls {
  display: flex;
  align-items: center;
  margin-right: 16px;
}

.shopee-quantity-btn {
  width: 32px;
  height: 32px;
  border: 1px solid #e5e5e5;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #757575;
}

.shopee-quantity-btn:hover:not(:disabled) {
  background: #f5f5f5;
  color: #222;
}

.shopee-quantity-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.shopee-quantity-input {
  width: 50px;
  height: 32px;
  border: 1px solid #e5e5e5;
  border-left: none;
  border-right: none;
  text-align: center;
  font-size: 14px;
  outline: none;
}

.shopee-stock-info {
  color: #757575;
  font-size: 14px;
}

/* Action Buttons */
.shopee-action-buttons {
  display: flex;
  gap: 12px;
  margin: 20px 0;
}

.shopee-wishlist-btn {
  width: 48px;
  height: 48px;
  border: 1px solid #ee4d2d;
  background: white;
  color: #ee4d2d;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.shopee-wishlist-btn:hover,
.shopee-wishlist-btn.active {
  background: #ee4d2d;
  color: white;
}

.shopee-chat-btn {
  flex: 1;
  height: 48px;
  border: 1px solid #ee4d2d;
  background: white;
  color: #ee4d2d;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.shopee-chat-btn:hover {
  background: #ee4d2d;
  color: white;
}

.shopee-cart-btn {
  flex: 1;
  height: 48px;
  border: 1px solid #ee4d2d;
  background: #ffebe8;
  color: #ee4d2d;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.2s ease;
}

.shopee-cart-btn:hover {
  background: #ee4d2d;
  color: white;
}

.shopee-buy-btn {
  flex: 1;
  height: 48px;
  border: 1px solid #ee4d2d;
  background: #ee4d2d;
  color: white;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.shopee-buy-btn:hover {
  background: #d73527;
  border-color: #d73527;
}

/* Store Section */
.shopee-store-section {
  background: white;
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
}

.shopee-store-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.shopee-store-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: #ee4d2d;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.shopee-store-initial {
  color: white;
  font-size: 24px;
  font-weight: 600;
}

.shopee-store-details {
  flex: 1;
}

.shopee-store-name {
  font-size: 16px;
  font-weight: 500;
  color: #222;
  margin-bottom: 4px;
}

.shopee-store-stats {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #757575;
}

.shopee-store-divider {
  color: #ccc;
}

.shopee-store-actions {
  display: flex;
  gap: 8px;
}

.shopee-store-chat,
.shopee-store-visit {
  padding: 8px 16px;
  border: 1px solid #ee4d2d;
  background: white;
  color: #ee4d2d;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.shopee-store-chat:hover,
.shopee-store-visit:hover {
  background: #ee4d2d;
  color: white;
}

/* Description Section */
.shopee-description-section {
  background: white;
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
}

.shopee-section-title {
  font-size: 16px;
  font-weight: 500;
  color: #222;
  margin-bottom: 16px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.shopee-description-content {
  color: #555;
  line-height: 1.6;
  font-size: 14px;
}

.shopee-product-specs {
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #e5e5e5;
}

.shopee-specs-title {
  font-size: 14px;
  font-weight: 500;
  color: #222;
  margin-bottom: 12px;
}

.shopee-specs-grid {
  display: grid;
  gap: 8px;
}

.shopee-spec-item {
  display: grid;
  grid-template-columns: 150px 1fr;
  gap: 16px;
  padding: 8px 0;
  border-bottom: 1px solid #f5f5f5;
}

.shopee-spec-label {
  color: #757575;
  font-size: 14px;
}

.shopee-spec-value {
  color: #222;
  font-size: 14px;
}

/* Reviews Section */
.shopee-reviews-section {
  background: white;
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
}

.shopee-reviews-summary {
  display: flex;
  align-items: center;
  gap: 24px;
  padding: 20px;
  background: #fafafa;
  border-radius: 4px;
}

.shopee-rating-overview {
  text-align: center;
}

.shopee-rating-score {
  display: flex;
  align-items: baseline;
  gap: 4px;
  margin-bottom: 8px;
}

.shopee-rating-number {
  font-size: 24px;
  font-weight: 500;
  color: #ee4d2d;
}

.shopee-rating-max {
  font-size: 14px;
  color: #757575;
}

.shopee-rating-stars-large {
  display: flex;
  gap: 2px;
  justify-content: center;
}

.shopee-reviews-count {
  color: #757575;
  font-size: 14px;
}

/* Review List */
.shopee-reviews-list {
  margin-top: 24px;
}

.shopee-review-item {
  border-bottom: 1px solid #f0f0f0;
  padding: 20px 0;
}

.shopee-review-item:last-child {
  border-bottom: none;
}

.shopee-review-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.shopee-reviewer-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #ee4d2d;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 16px;
  flex-shrink: 0;
}

.shopee-reviewer-info {
  flex: 1;
}

.shopee-reviewer-name {
  font-weight: 500;
  color: #222;
  font-size: 14px;
  margin-bottom: 4px;
}

.shopee-review-rating {
  display: flex;
  gap: 1px;
}

.shopee-review-date {
  color: #757575;
  font-size: 12px;
}

.shopee-review-content {
  color: #555;
  line-height: 1.5;
  font-size: 14px;
  margin-left: 52px;
}

.shopee-reviews-more {
  margin-top: 20px;
  text-align: center;
}

/* Related Products Section */
.shopee-related-section {
  background: white;
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
}

.shopee-related-products {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin-top: 16px;
}

.shopee-related-product-card {
  border: 1px solid #e5e5e5;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
  background: white;
}

.shopee-related-product-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.shopee-related-product-image {
  position: relative;
  width: 100%;
  height: 180px;
  background: #f8f8f8;
}

.shopee-related-discount-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background: #ff424f;
  color: white;
  padding: 2px 6px;
  border-radius: 2px;
  font-size: 10px;
  font-weight: 600;
}

.shopee-related-product-info {
  padding: 12px;
}

.shopee-related-product-name {
  font-size: 14px;
  color: #222;
  line-height: 1.3;
  margin-bottom: 8px;
  height: 36px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.shopee-related-product-price {
  margin-bottom: 8px;
}

.shopee-related-current-price {
  color: #ee4d2d;
  font-weight: 600;
  font-size: 16px;
}

.shopee-related-original-price {
  color: #999;
  text-decoration: line-through;
  font-size: 12px;
  margin-left: 8px;
}

.shopee-related-product-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.shopee-related-rating {
  display: flex;
  align-items: center;
  gap: 2px;
  color: #757575;
}

.shopee-related-sold {
  color: #757575;
}

/* Additional Utility Styles */
.shopee-layout * {
  box-sizing: border-box;
}

.shopee-layout img {
  max-width: 100%;
  height: auto;
}

/* Fix for image positioning */
.shopee-main-image img,
.shopee-thumbnail img {
  object-fit: contain;
  padding: 8px;
}

/* Hover effects */
.shopee-rating-text:hover,
.shopee-reviews-count:hover,
.shopee-sold-count:hover {
  text-decoration: underline;
}

/* Loading states */
.shopee-loading {
  opacity: 0.6;
  pointer-events: none;
}

/* Focus states for accessibility */
.shopee-quantity-btn:focus,
.shopee-action-buttons button:focus,
.shopee-store-actions button:focus {
  outline: 2px solid #ee4d2d;
  outline-offset: 2px;
}

.sellzio-action-buttons {
  display: flex;
  gap: 12px;
}

.sellzio-action-button {
  flex: 1;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.2s ease;
  cursor: pointer;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.sellzio-action-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.sellzio-add-to-cart {
  background: #f97316;
  color: white;
}

.sellzio-add-to-cart:hover {
  background: #ea580c;
}

.sellzio-wishlist-button,
.sellzio-share-button {
  background: white;
  color: #6b7280;
  border: 1px solid #d1d5db;
  min-width: 48px;
  flex: none;
}

.sellzio-wishlist-button:hover,
.sellzio-share-button:hover {
  background: #f9fafb;
  color: #374151;
}

.sellzio-wishlist-button.active {
  color: #ef4444;
  border-color: #ef4444;
}

.sellzio-description-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.sellzio-description-title {
  font-size: 20px;
  font-weight: 700;
  color: #111827;
  margin-bottom: 16px;
}

.sellzio-description-content {
  color: #374151;
  line-height: 1.6;
}

.sellzio-back-button {
  background: transparent;
  border: none;
  color: #6b7280;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 16px;
}

.sellzio-back-button:hover {
  background: #f3f4f6;
  color: #374151;
}

/* Mobile Responsive */
@media (max-width: 1024px) {
  .shopee-product-section {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .shopee-image-section {
    position: static;
  }

  .shopee-main-image {
    height: 300px;
  }
}

@media (max-width: 768px) {
  .shopee-container {
    padding: 0 12px;
  }

  .shopee-product-section {
    padding: 16px;
    margin-bottom: 12px;
  }

  .shopee-main-image {
    height: 280px;
  }

  .shopee-product-title {
    font-size: 18px;
  }

  .shopee-current-price {
    font-size: 24px;
  }

  .shopee-action-buttons {
    flex-wrap: wrap;
    gap: 8px;
  }

  .shopee-wishlist-btn {
    width: 44px;
    height: 44px;
  }

  .shopee-chat-btn,
  .shopee-cart-btn,
  .shopee-buy-btn {
    height: 44px;
    font-size: 13px;
  }

  .shopee-store-section,
  .shopee-description-section,
  .shopee-reviews-section {
    padding: 16px;
    margin-bottom: 12px;
  }

  .shopee-quantity-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .shopee-quantity-label {
    width: auto;
  }

  .shopee-delivery-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .shopee-delivery-label {
    width: auto;
  }

  .shopee-spec-item {
    grid-template-columns: 1fr;
    gap: 4px;
  }

  .shopee-reviews-summary {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }

  .shopee-related-products {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .shopee-related-product-image {
    height: 150px;
  }

  .shopee-review-content {
    margin-left: 0;
    margin-top: 8px;
  }
}

@media (max-width: 480px) {
  .shopee-breadcrumb {
    padding: 8px 0;
  }

  .shopee-breadcrumb-category,
  .shopee-breadcrumb-current {
    display: none;
  }

  .shopee-action-buttons {
    grid-template-columns: auto 1fr 1fr;
    display: grid;
    gap: 8px;
  }

  .shopee-wishlist-btn {
    grid-row: 1 / 3;
  }

  .shopee-chat-btn {
    grid-column: 2;
  }

  .shopee-cart-btn {
    grid-column: 3;
  }

  .shopee-buy-btn {
    grid-column: 2 / 4;
  }
}

"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import Image from "next/image"
import { ChevronLeft, Star, MapPin, Truck, Shield, Heart, Share2, ShoppingCart, Minus, Plus } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { toast } from "@/components/ui/use-toast"
import { SellzioHeader } from "@/components/themes/sellzio/sellzio-header"
import { useCart } from "@/hooks/use-cart"
import { sampleProducts, type Product } from "@/components/data/products"
import { getRelatedProducts } from "@/utils/product-helpers"
import {
  SellzioMallBadge,
  SellzioStarBadge,
  SellzioStarLiteBadge,
  SellzioCodBadge,
  SellzioDiscountCornerBadge,
  SellzioLiveCornerBadge,
  <PERSON>llzioTerlarisBadge,
  <PERSON>ll<PERSON>TermurahDiTokoBadge,
  SellzioKomisiXtraBadge,
  SellzioBadgeStyles,
} from "@/components/themes/sellzio/product-card/sellzio-badges"

interface SellzioProductDetailProps {
  productId: string
}

export function SellzioProductDetail({ productId }: SellzioProductDetailProps) {
  const router = useRouter()
  const { addToCart } = useCart()
  const [product, setProduct] = useState<Product | null>(null)
  const [loading, setLoading] = useState(true)
  const [selectedImage, setSelectedImage] = useState(0)
  const [quantity, setQuantity] = useState(1)
  const [isWishlisted, setIsWishlisted] = useState(false)

  // Determine badge type based on product data - sama seperti di halaman utama
  const getBadgeType = (product: Product) => {
    const sold = parseInt(product.sold.replace(/[^\d]/g, '')) || 0
    const rating = parseFloat(product.rating) || 0

    // Beberapa produk khusus untuk demo badge - sama seperti halaman utama
    if (product.id === 7 || product.id === 8) { // PlayStation & Xbox
      return "termurah"
    } else if (product.id === 15 || product.id === 16) { // Beberapa produk untuk komisi xtra
      return "komisi-xtra"
    } else if (product.isMall) {
      return "mall"
    } else if (sold > 1000) {
      return "star"
    } else if (rating > 4.5) {
      return "star-lite"
    } else if (sold > 500) {
      return "terlaris"
    }
    return "none"
  }

  useEffect(() => {
    // Simulasi fetch data produk dari sampleProducts
    const foundProduct = sampleProducts.find(p => p.id === parseInt(productId))
    if (foundProduct) {
      setProduct(foundProduct)
    }
    setLoading(false)
  }, [productId])

  const handleAddToCart = () => {
    if (!product) return
    
    addToCart({
      id: product.id.toString(),
      name: product.name,
      price: parseFloat(product.price.replace(/[^\d]/g, '')),
      image: product.image,
      quantity: quantity
    })
    
    toast({
      title: "Berhasil ditambahkan",
      description: `${product.name} telah ditambahkan ke keranjang`,
    })
  }

  const handleWishlist = () => {
    setIsWishlisted(!isWishlisted)
    toast({
      title: isWishlisted ? "Dihapus dari wishlist" : "Ditambahkan ke wishlist",
      description: `${product?.name} ${isWishlisted ? "dihapus dari" : "ditambahkan ke"} wishlist`,
    })
  }

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: product?.name,
        text: `Lihat produk ${product?.name} di Sellzio`,
        url: window.location.href,
      })
    } else {
      navigator.clipboard.writeText(window.location.href)
      toast({
        title: "Link disalin",
        description: "Link produk telah disalin ke clipboard",
      })
    }
  }

  const increaseQuantity = () => setQuantity(prev => prev + 1)
  const decreaseQuantity = () => setQuantity(prev => Math.max(1, prev - 1))

  if (loading) {
    return (
      <div className="sellzio-product-detail">
        <SellzioHeader />
        <div className="pt-16">
          <div className="sellzio-product-detail-container">
            <Skeleton className="h-8 w-32 mb-4" />
            <div className="sellzio-product-detail-grid">
              <Skeleton className="aspect-square w-full" />
              <div className="space-y-4">
                <Skeleton className="h-8 w-3/4" />
                <Skeleton className="h-6 w-1/2" />
                <Skeleton className="h-20 w-full" />
                <Skeleton className="h-12 w-full" />
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (!product) {
    return (
      <div className="sellzio-product-detail">
        <SellzioHeader />
        <div className="pt-16">
          <div className="sellzio-product-detail-container text-center py-12">
            <h1 className="text-2xl font-bold mb-4">Produk tidak ditemukan</h1>
            <p className="text-gray-600 mb-6">Produk yang Anda cari tidak ditemukan atau telah dihapus.</p>
            <Button onClick={() => router.push('/sellzio')}>
              Kembali ke Beranda
            </Button>
          </div>
        </div>
      </div>
    )
  }

  const rating = parseFloat(product.rating)
  const sold = parseInt(product.sold.replace(/[^\d]/g, ''))

  return (
    <div className="shopee-layout">
      <SellzioHeader />

      {/* Breadcrumb */}
      <div className="shopee-breadcrumb">
        <div className="shopee-container">
          <button
            className="shopee-back-link"
            onClick={() => router.back()}
          >
            <ChevronLeft className="h-4 w-4" />
            Kembali
          </button>
          <span className="shopee-breadcrumb-separator">></span>
          <span className="shopee-breadcrumb-category">{product.category}</span>
          <span className="shopee-breadcrumb-separator">></span>
          <span className="shopee-breadcrumb-subcategory">{product.subcategory}</span>
          <span className="shopee-breadcrumb-separator">></span>
          <span className="shopee-breadcrumb-current">{product.shortName || product.name.substring(0, 30)}...</span>
        </div>
      </div>

      {/* Main Content */}
      <div className="shopee-main-content">
        <div className="shopee-container">
          <div className="shopee-product-section">
            {/* Left: Product Images */}
            <div className="shopee-image-section">
              <div className="shopee-main-image">
                <Image
                  src={product.image}
                  alt={product.name}
                  fill
                  className="object-contain"
                  sizes="(max-width: 768px) 100vw, 40vw"
                />

                {/* Image Badges */}
                {product.isLive && <SellzioLiveCornerBadge />}
                {product.originalPrice && product.discount && <SellzioDiscountCornerBadge discount={product.discount} />}
              </div>

              {/* Thumbnail Gallery */}
              <div className="shopee-thumbnail-gallery">
                <div className="shopee-thumbnail active">
                  <Image
                    src={product.image}
                    alt={product.name}
                    fill
                    className="object-cover"
                  />
                </div>
                {/* Additional thumbnails can be added here */}
              </div>
            </div>

            {/* Right: Product Info */}
            <div className="shopee-product-info">
              <SellzioBadgeStyles />
              {/* Product Title */}
              <div className="shopee-product-header">
                <h1 className="shopee-product-title">
                  {/* Render badges based on priority - sama seperti product card */}
                  {(() => {
                    const badgeType = getBadgeType(product)
                    if (badgeType === "mall" || product.isMall) {
                      return <SellzioMallBadge />
                    } else if (badgeType === "star") {
                      return <SellzioStarBadge />
                    } else if (badgeType === "star-lite") {
                      return <SellzioStarLiteBadge />
                    }
                    return null
                  })()}
                  {product.name}
                </h1>

                {/* Special badges di bawah nama produk */}
                <div className="shopee-special-badges">
                  {(() => {
                    const badgeType = getBadgeType(product)
                    if (badgeType === "termurah") {
                      return <SellzioTermurahDiTokoBadge />
                    } else if (badgeType === "terlaris" || product.isTerlaris) {
                      return <SellzioTerlarisBadge />
                    } else if (badgeType === "komisi-xtra") {
                      return <SellzioKomisiXtraBadge />
                    }
                    return null
                  })()}
                </div>

                {/* Rating, Reviews & Sold */}
                <div className="shopee-product-stats">
                  <div className="shopee-rating">
                    <div className="shopee-stars">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <Star
                          key={star}
                          className={`h-4 w-4 ${star <= rating ? 'fill-orange-400 text-orange-400' : 'fill-gray-200 text-gray-200'}`}
                        />
                      ))}
                    </div>
                    <span className="shopee-rating-text">{rating}</span>
                  </div>

                  <div className="shopee-divider">|</div>

                  <div className="shopee-reviews">
                    <span className="shopee-reviews-count">{Math.floor(sold / 10)} Ulasan</span>
                  </div>

                  <div className="shopee-divider">|</div>

                  <div className="shopee-sold">
                    <span className="shopee-sold-count">{sold.toLocaleString()} Terjual</span>
                  </div>

                  {product.cod && (
                    <>
                      <div className="shopee-divider">|</div>
                      <div className="shopee-cod-badge">
                        <SellzioCodBadge />
                      </div>
                    </>
                  )}
                </div>
              </div>

              {/* Price Section */}
              <div className="shopee-price-section">
                <div className="shopee-price-container">
                  {product.originalPrice && (
                    <span className="shopee-original-price">{product.originalPrice}</span>
                  )}
                  <span className="shopee-current-price">{product.price}</span>
                  {product.discount && (
                    <span className="shopee-discount-percent">{product.discount} DISKON</span>
                  )}
                </div>
              </div>

              {/* Delivery & Services */}
              <div className="shopee-delivery-section">
                <div className="shopee-delivery-item">
                  <span className="shopee-delivery-label">Pengiriman</span>
                  <div className="shopee-delivery-info">
                    <Truck className="h-4 w-4 text-gray-500" />
                    <span>{product.shipping}</span>
                  </div>
                </div>

                {product.cod && (
                  <div className="shopee-delivery-item">
                    <span className="shopee-delivery-label">Pembayaran</span>
                    <div className="shopee-delivery-info">
                      <Shield className="h-4 w-4 text-green-500" />
                      <span>COD (Bayar di Tempat)</span>
                    </div>
                  </div>
                )}
              </div>

              {/* Quantity Selection */}
              <div className="shopee-quantity-section">
                <span className="shopee-quantity-label">Kuantitas</span>
                <div className="shopee-quantity-controls">
                  <button
                    className="shopee-quantity-btn"
                    onClick={decreaseQuantity}
                    disabled={quantity <= 1}
                  >
                    <Minus className="h-4 w-4" />
                  </button>
                  <input
                    type="text"
                    value={quantity}
                    readOnly
                    className="shopee-quantity-input"
                  />
                  <button
                    className="shopee-quantity-btn"
                    onClick={increaseQuantity}
                  >
                    <Plus className="h-4 w-4" />
                  </button>
                </div>
                <span className="shopee-stock-info">Tersedia 999+ buah</span>
              </div>

              {/* Action Buttons */}
              <div className="shopee-action-buttons">
                <button
                  className={`shopee-wishlist-btn ${isWishlisted ? "active" : ""}`}
                  onClick={handleWishlist}
                >
                  <Heart className={`h-5 w-5 ${isWishlisted ? "fill-current" : ""}`} />
                </button>

                <button
                  className="shopee-chat-btn"
                  onClick={() => toast({ title: "Chat", description: "Fitur chat akan segera hadir" })}
                >
                  Chat
                </button>

                <button
                  className="shopee-cart-btn"
                  onClick={handleAddToCart}
                >
                  <ShoppingCart className="h-5 w-5" />
                  Masukkan Keranjang
                </button>

                <button
                  className="shopee-buy-btn"
                  onClick={() => toast({ title: "Beli Sekarang", description: "Fitur beli langsung akan segera hadir" })}
                >
                  Beli Sekarang
                </button>
              </div>
            </div>
          </div>

          {/* Store Info Section */}
          <div className="shopee-store-section">
            <div className="shopee-store-info">
              <div className="shopee-store-avatar">
                <div className="shopee-store-initial">
                  {product.storeName.charAt(0)}
                </div>
              </div>

              <div className="shopee-store-details">
                <h3 className="shopee-store-name">{product.storeName}</h3>
                <div className="shopee-store-stats">
                  <span className="shopee-store-stat">Online 2 jam lalu</span>
                  <span className="shopee-store-divider">|</span>
                  <span className="shopee-store-stat">
                    <MapPin className="h-3 w-3" />
                    {product.address.city}
                  </span>
                </div>
              </div>

              <div className="shopee-store-actions">
                <button className="shopee-store-chat">Chat</button>
                <button className="shopee-store-visit">Lihat Toko</button>
              </div>
            </div>
          </div>

          {/* Product Description */}
          <div className="shopee-description-section">
            <h2 className="shopee-section-title">DESKRIPSI PRODUK</h2>
            <div className="shopee-description-content">
              <p>
                {product.name} adalah produk berkualitas tinggi yang tersedia di {product.storeName}.
                Produk ini telah terjual sebanyak {sold.toLocaleString()} unit dengan rating {rating} bintang
                dari para pembeli. Tersedia pengiriman {product.shipping.toLowerCase()} ke seluruh Indonesia.
                {product.cod && " Mendukung pembayaran COD (Cash on Delivery) untuk kemudahan berbelanja Anda."}
              </p>

              <div className="shopee-product-specs">
                <h3 className="shopee-specs-title">Spesifikasi Produk</h3>
                <div className="shopee-specs-grid">
                  <div className="shopee-spec-item">
                    <span className="shopee-spec-label">Kategori</span>
                    <span className="shopee-spec-value">{product.category}</span>
                  </div>
                  <div className="shopee-spec-item">
                    <span className="shopee-spec-label">Berat</span>
                    <span className="shopee-spec-value">1000 gram</span>
                  </div>
                  <div className="shopee-spec-item">
                    <span className="shopee-spec-label">Asal Pengiriman</span>
                    <span className="shopee-spec-value">{product.address.city}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Reviews Section */}
          <div className="shopee-reviews-section">
            <h2 className="shopee-section-title">PENILAIAN PRODUK</h2>
            <div className="shopee-reviews-summary">
              <div className="shopee-rating-overview">
                <div className="shopee-rating-score">
                  <span className="shopee-rating-number">{rating}</span>
                  <span className="shopee-rating-max">dari 5</span>
                </div>
                <div className="shopee-rating-stars-large">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <Star
                      key={star}
                      className={`h-5 w-5 ${star <= rating ? 'fill-orange-400 text-orange-400' : 'fill-gray-200 text-gray-200'}`}
                    />
                  ))}
                </div>
              </div>

              <div className="shopee-reviews-count">
                <span>{Math.floor(sold / 10)} ulasan</span>
              </div>
            </div>

            {/* Review List */}
            <div className="shopee-reviews-list">
              {[1, 2, 3].map((reviewIndex) => (
                <div key={reviewIndex} className="shopee-review-item">
                  <div className="shopee-review-header">
                    <div className="shopee-reviewer-avatar">
                      <span>{String.fromCharCode(65 + reviewIndex)}</span>
                    </div>
                    <div className="shopee-reviewer-info">
                      <div className="shopee-reviewer-name">User{reviewIndex}***</div>
                      <div className="shopee-review-rating">
                        {[1, 2, 3, 4, 5].map((star) => (
                          <Star
                            key={star}
                            className={`h-3 w-3 ${star <= rating ? 'fill-orange-400 text-orange-400' : 'fill-gray-200 text-gray-200'}`}
                          />
                        ))}
                      </div>
                    </div>
                    <div className="shopee-review-date">
                      {reviewIndex} hari lalu
                    </div>
                  </div>
                  <div className="shopee-review-content">
                    Produk sangat bagus, sesuai dengan deskripsi. Pengiriman cepat dan packaging aman.
                    {reviewIndex === 1 && "Kualitas build sangat solid dan performa memuaskan."}
                    {reviewIndex === 2 && "Harga sebanding dengan kualitas yang didapat."}
                    {reviewIndex === 3 && "Recommended untuk yang mencari produk berkualitas."}
                  </div>
                </div>
              ))}

              <div className="shopee-reviews-more">
                <Button variant="outline" className="w-full">
                  Lihat Semua {Math.floor(sold / 10)} Ulasan
                </Button>
              </div>
            </div>
          </div>

          {/* Related Products Section */}
          <div className="shopee-related-section">
            <h2 className="shopee-section-title">PRODUK TERKAIT</h2>
            <div className="shopee-related-products">
              {getRelatedProducts(product, 4)
                .map((relatedProduct) => (
                  <div
                    key={relatedProduct.id}
                    className="shopee-related-product-card"
                    onClick={() => router.push(`/products/${relatedProduct.id}`)}
                  >
                    <div className="shopee-related-product-image">
                      <Image
                        src={relatedProduct.image}
                        alt={relatedProduct.name}
                        fill
                        className="object-contain"
                        sizes="(max-width: 768px) 50vw, 25vw"
                      />
                      {relatedProduct.discount && (
                        <div className="shopee-related-discount-badge">
                          -{relatedProduct.discount}
                        </div>
                      )}
                    </div>
                    <div className="shopee-related-product-info">
                      <h3 className="shopee-related-product-name">
                        {relatedProduct.shortName || relatedProduct.name.substring(0, 50)}...
                      </h3>
                      <div className="shopee-related-product-price">
                        <span className="shopee-related-current-price">{relatedProduct.price}</span>
                        {relatedProduct.originalPrice && (
                          <span className="shopee-related-original-price">{relatedProduct.originalPrice}</span>
                        )}
                      </div>
                      <div className="shopee-related-product-stats">
                        <div className="shopee-related-rating">
                          <Star className="h-3 w-3 fill-orange-400 text-orange-400" />
                          <span>{relatedProduct.rating}</span>
                        </div>
                        <div className="shopee-related-sold">
                          {relatedProduct.sold} terjual
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

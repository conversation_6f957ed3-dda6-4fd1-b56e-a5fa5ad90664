"use client"

import React from 'react'
import { ArrowLeft, CheckCircle, Clock, XCircle, RefreshCw } from 'lucide-react'
import type { Payment } from '@/lib/models/payment'

interface SellzioPaymentStatusPageProps {
  payment: Payment | null
  onBack: () => void
  onRefreshStatus: () => void
  onCancelPayment: () => void
}

export const SellzioPaymentStatusPage: React.FC<SellzioPaymentStatusPageProps> = ({
  payment,
  onBack,
  onRefreshStatus,
  onCancelPayment
}) => {
  const formatPrice = (price: number) => {
    return `Rp${price.toLocaleString('id-ID')}`
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('id-ID', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getStatusIcon = () => {
    if (!payment) return null

    switch (payment.status) {
      case 'completed':
        return <CheckCircle size={64} className="status-icon success" />
      case 'pending':
        return <Clock size={64} className="status-icon pending" />
      case 'processing':
        return <div className="status-icon processing"><div className="spinner"></div></div>
      case 'failed':
      case 'expired':
        return <XCircle size={64} className="status-icon error" />
      default:
        return <Clock size={64} className="status-icon pending" />
    }
  }

  const getStatusTitle = () => {
    if (!payment) return 'Status Tidak Diketahui'

    switch (payment.status) {
      case 'completed':
        return 'Pembayaran Berhasil'
      case 'pending':
        return 'Menunggu Pembayaran'
      case 'processing':
        return 'Pembayaran Diproses'
      case 'failed':
        return 'Pembayaran Gagal'
      case 'expired':
        return 'Pembayaran Kedaluwarsa'
      default:
        return 'Status Tidak Diketahui'
    }
  }

  const getStatusMessage = () => {
    if (!payment) return 'Tidak dapat memuat status pembayaran'

    switch (payment.status) {
      case 'completed':
        return 'Pembayaran Anda telah berhasil diverifikasi. Pesanan sedang diproses.'
      case 'pending':
        return 'Silakan selesaikan pembayaran sesuai instruksi yang diberikan.'
      case 'processing':
        return 'Pembayaran Anda sedang diverifikasi. Mohon tunggu beberapa saat.'
      case 'failed':
        return 'Pembayaran gagal diproses. Silakan coba lagi atau gunakan metode lain.'
      case 'expired':
        return 'Batas waktu pembayaran telah berakhir. Silakan buat pesanan baru.'
      default:
        return 'Status pembayaran tidak diketahui.'
    }
  }

  if (!payment) {
    return (
      <div className="sellzio-payment-status-page">
        <div className="sellzio-status-container">
          <div className="sellzio-status-card">
            <div className="sellzio-status-header">
              <button className="sellzio-back-btn" onClick={onBack}>
                <ArrowLeft size={20} />
              </button>
              <h1>Status Pembayaran</h1>
            </div>
            <div className="sellzio-status-content">
              <div className="sellzio-status-icon-container">
                <XCircle size={64} className="status-icon error" />
              </div>
              <h2>Pembayaran Tidak Ditemukan</h2>
              <p>Data pembayaran tidak dapat ditemukan. Silakan periksa kembali.</p>
              <button className="sellzio-primary-btn" onClick={onBack}>
                Kembali ke Beranda
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="sellzio-payment-status-page">
      <div className="sellzio-status-container">
        <div className="sellzio-status-card">
          <div className="sellzio-status-header">
            <button className="sellzio-back-btn" onClick={onBack}>
              <ArrowLeft size={20} />
            </button>
            <h1>Status Pembayaran</h1>
          </div>

          <div className="sellzio-status-content">
            <div className="sellzio-status-icon-container">
              {getStatusIcon()}
            </div>

            <h2 className="sellzio-status-title">{getStatusTitle()}</h2>
            <p className="sellzio-status-message">{getStatusMessage()}</p>

            <div className="sellzio-payment-details">
              <div className="sellzio-detail-row">
                <span className="sellzio-detail-label">Order ID</span>
                <span className="sellzio-detail-value">{payment.orderId}</span>
              </div>
              <div className="sellzio-detail-row">
                <span className="sellzio-detail-label">Jumlah</span>
                <span className="sellzio-detail-value">{formatPrice(payment.amount)}</span>
              </div>
              <div className="sellzio-detail-row">
                <span className="sellzio-detail-label">Metode</span>
                <span className="sellzio-detail-value">{payment.paymentMethod.name}</span>
              </div>
              <div className="sellzio-detail-row">
                <span className="sellzio-detail-label">Waktu</span>
                <span className="sellzio-detail-value">{formatDate(payment.createdAt)}</span>
              </div>
              {payment.expiryTime && payment.status === 'pending' && (
                <div className="sellzio-detail-row">
                  <span className="sellzio-detail-label">Batas Waktu</span>
                  <span className="sellzio-detail-value">{formatDate(payment.expiryTime)}</span>
                </div>
              )}
            </div>

            {payment.paymentInstructions && payment.status === 'pending' && (
              <div className="sellzio-payment-instructions">
                <h3>Cara Pembayaran</h3>
                <ol>
                  {payment.paymentInstructions.map((instruction, index) => (
                    <li key={index}>{instruction}</li>
                  ))}
                </ol>
              </div>
            )}

            <div className="sellzio-status-actions">
              {payment.status === 'pending' && (
                <>
                  <button className="sellzio-secondary-btn" onClick={onRefreshStatus}>
                    <RefreshCw size={16} />
                    Refresh Status
                  </button>
                  <button className="sellzio-danger-btn" onClick={onCancelPayment}>
                    Batalkan
                  </button>
                </>
              )}
              <button className="sellzio-primary-btn" onClick={onBack}>
                {payment.status === 'completed' ? 'Lihat Pesanan' : 'Kembali ke Beranda'}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <style jsx>{`
      .sellzio-payment-status-page {
        min-height: 100vh;
        background: #f5f5f5;
        color: #333;
        font-family: 'Roboto', Arial, sans-serif;
      }

      .sellzio-status-container {
        padding: 20px;
        max-width: 480px;
        margin: 0 auto;
      }

      .sellzio-status-card {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        overflow: hidden;
      }

      .sellzio-status-header {
        display: flex;
        align-items: center;
        padding: 16px 20px;
        border-bottom: 1px solid #f0f0f0;
        background: white;
      }

      .sellzio-back-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        background: none;
        border: none;
        border-radius: 50%;
        cursor: pointer;
        transition: background-color 0.2s ease;
        color: #333;
        margin-right: 12px;
      }

      .sellzio-back-btn:hover {
        background: #f5f5f5;
      }

      .sellzio-status-header h1 {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin: 0;
      }

      .sellzio-status-content {
        padding: 32px 20px;
        text-align: center;
      }

      .sellzio-status-icon-container {
        margin-bottom: 24px;
        display: flex;
        justify-content: center;
      }

      .status-icon {
        display: block;
      }

      .status-icon.success {
        color: #27ae60;
      }

      .status-icon.pending {
        color: #f39c12;
      }

      .status-icon.error {
        color: #e74c3c;
      }

      .status-icon.processing {
        width: 64px;
        height: 64px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .spinner {
        width: 48px;
        height: 48px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #3498db;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      .sellzio-status-title {
        font-size: 24px;
        font-weight: 700;
        color: #333;
        margin: 0 0 12px 0;
      }

      .sellzio-status-message {
        font-size: 16px;
        color: #666;
        line-height: 1.5;
        margin: 0 0 32px 0;
      }

      .sellzio-payment-details {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 24px;
        text-align: left;
      }

      .sellzio-detail-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
      }

      .sellzio-detail-row:last-child {
        margin-bottom: 0;
      }

      .sellzio-detail-label {
        font-size: 14px;
        color: #666;
      }

      .sellzio-detail-value {
        font-size: 14px;
        font-weight: 500;
        color: #333;
      }

      .sellzio-payment-instructions {
        background: #fff5f5;
        border: 1px solid #fee;
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 24px;
        text-align: left;
      }

      .sellzio-payment-instructions h3 {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin: 0 0 12px 0;
      }

      .sellzio-payment-instructions ol {
        margin: 0;
        padding-left: 20px;
      }

      .sellzio-payment-instructions li {
        font-size: 14px;
        color: #666;
        margin-bottom: 8px;
        line-height: 1.4;
      }

      .sellzio-status-actions {
        display: flex;
        flex-direction: column;
        gap: 12px;
      }

      .sellzio-primary-btn,
      .sellzio-secondary-btn,
      .sellzio-danger-btn {
        padding: 12px 24px;
        border-radius: 8px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
      }

      .sellzio-primary-btn {
        background: #ee4d2d;
        color: white;
      }

      .sellzio-primary-btn:hover {
        background: #d73527;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(238, 77, 45, 0.3);
      }

      .sellzio-secondary-btn {
        background: #f8f9fa;
        color: #333;
        border: 1px solid #e5e5e5;
      }

      .sellzio-secondary-btn:hover {
        background: #e9ecef;
      }

      .sellzio-danger-btn {
        background: #dc3545;
        color: white;
      }

      .sellzio-danger-btn:hover {
        background: #c82333;
      }

      @media (max-width: 768px) {
        .sellzio-status-container {
          padding: 12px;
        }

        .sellzio-status-content {
          padding: 24px 16px;
        }

        .sellzio-status-title {
          font-size: 20px;
        }

        .sellzio-status-message {
          font-size: 14px;
        }
      }
    `}</style>
  )
}

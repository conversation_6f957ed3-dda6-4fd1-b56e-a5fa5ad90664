"use client"

import React, { useState, useEffect, useRef } from 'react'
import { Search, ShoppingCart, MessageCircle, ArrowLeft, X, Filter } from 'lucide-react'

interface SellzioHeaderProps {
  onSearchFocus?: () => void
  onSearchBlur?: () => void
  onSearchChange?: (value: string) => void
  onSearchExecute?: (query: string) => void
  onClearSearch?: () => void
  searchValue?: string
  isExpanded?: boolean
  onToggleExpanded?: () => void
  showFilterIcon?: boolean
  onFilterClick?: () => void
  filterBadgeCount?: number
  onCartClick?: () => void
  cartCount?: number
  onChatClick?: () => void
  chatCount?: number
}

export const SellzioHeader: React.FC<SellzioHeaderProps> = ({
  onSearchFocus,
  onSearchBlur,
  onSearchChange,
  onSearchExecute,
  onClearSearch,
  searchValue = '',
  isExpanded = false,
  onToggleExpanded,
  showFilterIcon = false,
  onFilterClick,
  filterBadgeCount = 0,
  onCartClick,
  cartCount = 0,
  onChatClick,
  chatCount = 0
}) => {
  const [inputValue, setInputValue] = useState('')
  const [showClearIcon, setShowClearIcon] = useState(false)
  const [placeholderIndex, setPlaceholderIndex] = useState(0)
  const [isClient, setIsClient] = useState(false)
  const [isInitialized, setIsInitialized] = useState(false)
  const inputRef = useRef<HTMLInputElement>(null)

  // Prevent hydration mismatch and ensure proper initialization
  useEffect(() => {
    setIsClient(true)
    setIsInitialized(true)
    // Only set initial values on client mount
    if (searchValue) {
      setInputValue(searchValue)
      setShowClearIcon(searchValue.length > 0)
    }
  }, [])

  // Sync inputValue with searchValue prop when it changes (only after initialization)
  useEffect(() => {
    if (isInitialized) {
      setInputValue(searchValue)
      setShowClearIcon(searchValue.length > 0)
    }
  }, [searchValue, isInitialized])

  // Placeholder texts sesuai docs/facet.html
  const placeholderTexts = [
    "Cari produk, brand, atau toko",
    "Sepatu sneakers pria",
    "Tas wanita branded",
    "Smartphone terbaru",
    "Laptop gaming murah",
    "Headphone wireless",
    "Jam tangan pria",
    "Dress wanita cantik",
    "Kamera mirrorless",
    "Power bank 10000mah",
    "Earbuds bluetooth",
    "Smartwatch fitness",
    "Keyboard mechanical",
    "Mouse gaming",
    "Blender portable"
  ]

  // Animasi placeholder
  useEffect(() => {
    if (!inputValue && !isExpanded) {
      const interval = setInterval(() => {
        setPlaceholderIndex((prev) => (prev + 1) % placeholderTexts.length)
      }, 3000)
      return () => clearInterval(interval)
    }
  }, [inputValue, isExpanded, placeholderTexts.length])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setInputValue(value)
    setShowClearIcon(value.length > 0)
    onSearchChange?.(value)
  }

  const handleInputFocus = () => {
    onSearchFocus?.()
    onToggleExpanded?.()
  }

  const handleInputBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    // Hanya trigger blur jika tidak dalam expanded mode
    if (!isExpanded) {
      onSearchBlur?.()
    }
  }

  const handleClearSearch = () => {
    setInputValue('')
    setShowClearIcon(false)
    onSearchChange?.('')
    onClearSearch?.() // Notify parent about clear action
    inputRef.current?.focus()
  }

  const handleBackClick = () => {
    setInputValue('')
    setShowClearIcon(false)
    onSearchChange?.('')
    onClearSearch?.() // Notify parent about clear action
    onToggleExpanded?.()
  }

  const handleSearchClick = () => {
    // Handle search action - execute search with current input value
    if (inputValue.trim()) {
      onSearchExecute?.(inputValue.trim())
      console.log('Search clicked:', inputValue)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      // Handle Enter key press - execute search
      if (inputValue.trim()) {
        onSearchExecute?.(inputValue.trim())
        console.log('Enter pressed:', inputValue)
      }
    }
  }

  if (isExpanded) {
    return (
      <div className="search-expanded">
        <div className="search-container">
          <button className="back-btn" onClick={handleBackClick}>
            <ArrowLeft size={20} />
          </button>
          <div className="search-input-wrapper">
            <input
              ref={inputRef}
              type="text"
              className="search-input"
              value={inputValue}
              onChange={handleInputChange}
              onBlur={handleInputBlur}
              onKeyPress={handleKeyPress}
              placeholder="Cari produk, brand, atau toko"
              autoFocus
            />
            {isClient && showClearIcon && (
              <button className="clear-search-icon" onClick={handleClearSearch}>
                <X size={12} />
              </button>
            )}
          </div>
          {!showFilterIcon ? (
            <button className="expanded-search-icon" onClick={handleSearchClick}>
              <Search size={20} />
            </button>
          ) : (
            <button className="filter-icon" onClick={onFilterClick}>
              <i className="fa fa-filter"></i>
              {filterBadgeCount > 0 && (
                <span className="filter-badge">{filterBadgeCount}</span>
              )}
            </button>
          )}
        </div>
      </div>
    )
  }

  return (
    <header className="header">
      <div className="search-container">
        <div className="search-input-wrapper">
          <input
            ref={inputRef}
            type="text"
            className="search-input"
            value={inputValue}
            onChange={handleInputChange}
            onFocus={handleInputFocus}
            onBlur={handleInputBlur}
            onKeyPress={handleKeyPress}
            placeholder=""
          />
          
          {/* Animated placeholder */}
          {!inputValue && !isExpanded && isClient && (
            <div className="search-placeholder">
              <div className="placeholder-static">
                Cari di Sellzio
              </div>
              <div className="placeholder-dynamic">
                {placeholderTexts.map((text, index) => (
                  <div
                    key={index}
                    className={`placeholder-text ${index === placeholderIndex ? 'active' : ''}`}
                    style={{
                      animationDelay: `${index * 3}s`
                    }}
                  >
                    {text}
                  </div>
                ))}
              </div>
            </div>
          )}

          {isClient && showClearIcon && (
            <button className="clear-search-icon" onClick={handleClearSearch}>
              <X size={12} />
            </button>
          )}
          
          <button className="search-icon" onClick={handleSearchClick}>
            <Search size={20} />
          </button>
        </div>

        {/* Header Icons */}
        <div className="header-icons">
          {/* Cart icon with badge */}
          <div className="cart-icon" onClick={onCartClick}>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="22"
              height="22"
              viewBox="0 0 576 512"
              className="cursor-pointer"
            >
              <path
                fill="#ee4d2d"
                stroke="white"
                strokeWidth="30"
                d="M0 24C0 10.7 10.7 0 24 0H69.5c22 0 41.5 12.8 50.6 32h411c26.3 0 45.5 25 38.6 50.4l-41 152.3c-8.5 31.4-37 53.3-69.5 53.3H170.7l5.4 28.5c2.2 11.3 12.1 19.5 23.6 19.5H488c13.3 0 24 10.7 24 24s-10.7 24-24 24H199.7c-34.6 0-64.3-24.6-70.7-58.5L77.4 54.5c-.7-3.8-4-6.5-7.9-6.5H24C10.7 48 0 37.3 0 24zM128 464a48 48 0 1 1 96 0 48 48 0 1 1 -96 0zm336-48a48 48 0 1 1 0 96 48 48 0 1 1 0-96z"
              />
            </svg>
            <span className="cart-badge">{cartCount}</span>
          </div>

          {/* Chat icon with badge */}
          <div className="chat-icon" onClick={onChatClick}>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="22"
              height="22"
              viewBox="0 0 512 512"
              className="cursor-pointer"
            >
              <path
                fill="#ee4d2d"
                stroke="white"
                strokeWidth="30"
                d="M256 448c141.4 0 256-93.1 256-208S397.4 32 256 32S0 125.1 0 240c0 45.1 17.7 86.8 47.7 120.9c-1.9 24.5-11.4 46.3-21.4 62.9c-5.5 9.2-11.1 16.6-15.2 21.6c-2.1 2.5-3.7 4.4-4.9 5.7c-.6 .6-1 1.1-1.3 1.4l-.3 .3 0 0 0 0 0 0 0 0c-4.6 4.6-5.9 11.4-3.4 17.4c2.5 6 8.3 9.9 14.8 9.9c28.7 0 57.6-8.9 81.6-19.3c22.9-10 42.4-21.9 54.3-30.6c31.8 11.5 67 17.9 104.1 17.9z"
              />
            </svg>
            <div className="chat-dots">
              <div className="dot"></div>
              <div className="dot"></div>
              <div className="dot"></div>
            </div>
            <span className="chat-badge">{chatCount}</span>
          </div>
        </div>
      </div>
    </header>
  )
}

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;
  }
  
  /* Style khusus untuk tombol di dark mode */
  .dark button[data-component-name="_c"] {
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@keyframes placeholderAnimation {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  1%,
  5% {
    opacity: 1;
    transform: translateY(0);
  }
  6%,
  100% {
    opacity: 0;
    transform: translateY(-20px);
  }
}

.animate-placeholder {
  animation: placeholderAnimation 12s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.animate-shimmer {
  animation: shimmer 2s infinite linear;
}

/* Animasi equalizer untuk LiveBadge */
@keyframes equalizer-1 {
  0%,
  100% {
    height: 1px;
  }
  50% {
    height: 4px;
  }
}

@keyframes equalizer-2 {
  0%,
  100% {
    height: 3px;
  }
  50% {
    height: 1px;
  }
}

@keyframes equalizer-3 {
  0%,
  100% {
    height: 2px;
  }
  50% {
    height: 5px;
  }
}

.animate-equalizer-1 {
  animation: equalizer-1 0.8s ease-in-out infinite;
}

.animate-equalizer-2 {
  animation: equalizer-2 0.7s ease-in-out infinite;
}

.animate-equalizer-3 {
  animation: equalizer-3 0.9s ease-in-out infinite;
}

/* Custom Date Picker Styling */
.custom-date-picker {
  position: relative;
  width: 100%;
}

.custom-date-picker input[type="date"] {
  width: 100%;
  padding: 0.5rem 0.75rem 0.5rem 2.5rem;
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  border: 1px solid hsl(var(--input));
  border-radius: var(--radius);
  font-size: 0.875rem;
  line-height: 1.25rem;
  outline: none;
  appearance: none;
  -webkit-appearance: none;
}

.custom-date-picker input[type="date"]::-webkit-calendar-picker-indicator {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  opacity: 0;
  cursor: pointer;
}

.custom-date-picker input[type="date"]:focus {
  border-color: hsl(var(--ring));
  box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2);
}

.custom-date-picker .calendar-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: hsl(var(--muted-foreground));
  pointer-events: none;
}

/* Custom Calendar Styling */
::-webkit-calendar-picker-indicator {
  filter: invert(0.5);
}

.dark ::-webkit-calendar-picker-indicator {
  filter: invert(0.8);
}

/* Custom Date Picker Dialog Styling */
::-webkit-datetime-edit {
  color: hsl(var(--foreground));
}

::-webkit-datetime-edit-fields-wrapper {
  padding: 0;
}

::-webkit-datetime-edit-text {
  padding: 0 0.2em;
}

::-webkit-datetime-edit-month-field,
::-webkit-datetime-edit-day-field,
::-webkit-datetime-edit-year-field {
  padding: 0 0.2em;
}

::-webkit-inner-spin-button {
  display: none;
}

/* Custom Month/Year Dropdown Styling */
.date-picker-dropdown {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.date-picker-dropdown select {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  border: 1px solid hsl(var(--input));
  border-radius: var(--radius);
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  outline: none;
}

.date-picker-dropdown select:focus {
  border-color: hsl(var(--ring));
  box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2);
}

/* Custom Calendar Table Styling */
.date-picker-calendar {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0.25rem;
}

.date-picker-calendar th {
  padding: 0.25rem;
  text-align: center;
  font-weight: 500;
  font-size: 0.75rem;
  color: hsl(var(--muted-foreground));
}

.date-picker-calendar td {
  padding: 0;
  text-align: center;
}

.date-picker-calendar button {
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  border: none;
  background: transparent;
  color: hsl(var(--foreground));
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s, color 0.2s;
}

.date-picker-calendar button:hover {
  background-color: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
}

.date-picker-calendar button.selected {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

.date-picker-calendar button.today {
  font-weight: bold;
  border: 1px solid hsl(var(--primary));
}

.date-picker-calendar button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Custom Date Picker Modal */
.date-picker-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
  background-color: rgba(0, 0, 0, 0.5);
}

.date-picker-modal-content {
  background-color: hsl(var(--background));
  border-radius: var(--radius);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  padding: 1rem;
  width: 300px;
  max-width: 90%;
}

.date-picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.date-picker-title {
  font-weight: 600;
  font-size: 1rem;
}

.date-picker-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  margin-top: 1rem;
}

.date-picker-btn {
  padding: 0.5rem 1rem;
  border-radius: var(--radius);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.date-picker-btn-primary {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  border: none;
}

.date-picker-btn-secondary {
  background-color: transparent;
  color: hsl(var(--foreground));
  border: 1px solid hsl(var(--input));
}

.date-picker-btn-primary:hover {
  background-color: hsl(var(--primary) / 0.9);
}

.date-picker-btn-secondary:hover {
  background-color: hsl(var(--accent));
}

/* Hide scrollbar but keep scroll functionality */
.scrollbar-hide {
  /* Firefox */
  scrollbar-width: none;
  /* Internet Explorer 10+ */
  -ms-overflow-style: none;
}

/* Safari and Chrome */
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Import Sellzio Styles */
@import url('../components/themes/sellzio/sellzio-styles.css');
@import url('../components/sellzio/product-detail.css');

/* Prevent Tailwind CSS from overriding Sellzio header styles */
.header,
header.header {
  all: unset !important;
  background-color: #ee4d2d !important;
  padding: 10px 15px !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  z-index: 1000 !important;
  transition: all 0.3s ease !important;
  box-sizing: border-box !important;
  min-height: 60px !important;
  display: block !important;
  opacity: 1 !important;
  visibility: visible !important;
}

/* Ensure header children maintain their styles */
.header *,
header.header * {
  box-sizing: border-box !important;
}

/* Prevent layout shift during hydration */
html {
  scroll-behavior: smooth;
}

body {
  margin: 0 !important;
  padding: 0 !important;
  overflow-x: hidden;
}

/* Ensure consistent rendering across page loads */
.shopee-layout,
.sellzio-product-detail {
  opacity: 1 !important;
  visibility: visible !important;
}

/* Force header to be visible immediately */
.header,
header.header {
  transform: none !important;
  animation: none !important;
}
